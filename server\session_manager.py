"""
会话管理系统
"""
import asyncio
import uuid
from datetime import datetime
from typing import Dict, Optional, List
from threading import Lock

from models import Session, SessionStatus, VideoProcessor
from logging_config import get_logger, get_error_handler, log_errors


logger = get_logger(__name__)
api_error_handler = get_error_handler("api")
video_processing_error_handler = get_error_handler("video_processing")


class ProcessorFactory:
    """处理器工厂类"""
    
    _processors: Dict[str, type] = {}
    
    @classmethod
    def register_processor(cls, name: str, processor_class: type) -> None:
        """注册处理器类型"""
        cls._processors[name] = processor_class
        logger.info(f"Registered processor: {name}")
    
    @classmethod
    def create_processor(cls, analysis_type: str) -> VideoProcessor:
        """根据类型创建处理器实例"""
        if analysis_type not in cls._processors:
            error_msg = f"Unknown processor type: {analysis_type}"
            logger.error(error_msg)
            raise ValueError(error_msg)
        
        processor_class = cls._processors[analysis_type]
        logger.debug(f"Creating processor instance of type: {analysis_type}")
        return processor_class()
    
    @classmethod
    def get_available_types(cls) -> List[str]:
        """获取所有可用的处理器类型"""
        types = list(cls._processors.keys())
        logger.debug(f"Available processor types: {types}")
        return types


class SessionManager:
    """会话管理器"""
    
    def __init__(self, session_timeout: int = 3600, max_concurrent_sessions: int = 100):
        """
        初始化会话管理器
        
        Args:
            session_timeout: 会话超时时间（秒）
            max_concurrent_sessions: 最大并发会话数
        """
        self._sessions: Dict[str, Session] = {}
        self._session_timeout = session_timeout
        self._max_concurrent_sessions = max_concurrent_sessions
        self._lock = Lock()  # 保护会话字典的线程安全
        self._cleanup_task: Optional[asyncio.Task] = None
        self._running = False
        
        logger.info(f"SessionManager initialized with timeout={session_timeout}s, max_sessions={max_concurrent_sessions}")
    
    def update_config(self, session_timeout: int = None, max_concurrent_sessions: int = None):
        """
        更新会话管理器配置
        
        Args:
            session_timeout: 会话超时时间（秒）
            max_concurrent_sessions: 最大并发会话数
        """
        with self._lock:
            if session_timeout is not None:
                self._session_timeout = session_timeout
                logger.info(f"Updated session timeout to {session_timeout}s")
            
            if max_concurrent_sessions is not None:
                self._max_concurrent_sessions = max_concurrent_sessions
                logger.info(f"Updated max concurrent sessions to {max_concurrent_sessions}")
    
    def get_config(self) -> Dict[str, int]:
        """
        获取当前配置
        
        Returns:
            Dict: 包含当前配置的字典
        """
        with self._lock:
            return {
                "session_timeout": self._session_timeout,
                "max_concurrent_sessions": self._max_concurrent_sessions
            }
    
    def start(self) -> None:
        """启动会话管理器"""
        if not self._running:
            self._running = True
            self._cleanup_task = asyncio.create_task(self._cleanup_loop())
            logger.info("SessionManager started")
    
    async def stop(self) -> None:
        """停止会话管理器"""
        self._running = False
        
        # 取消清理任务
        if self._cleanup_task and not self._cleanup_task.done():
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                logger.info("Cleanup loop cancelled")
            except Exception as e:
                logger.error(f"Error cancelling cleanup task: {e}")
        
        # 清理所有会话
        await self._cleanup_all_sessions()
        logger.info("SessionManager stopped")
    
    @log_errors("api", "create_session")
    def create_session(self, analysis_type: str) -> str:
        """
        创建新的视频处理会话
        
        Args:
            analysis_type: 分析类型
            
        Returns:
            str: 会话密钥
            
        Raises:
            ValueError: 当分析类型不支持或达到最大会话数时
        """
        with self._lock:
            # 检查是否达到最大会话数
            current_sessions = len(self._sessions)
            if current_sessions >= self._max_concurrent_sessions:
                error_msg = f"Maximum concurrent sessions ({self._max_concurrent_sessions}) reached"
                logger.warning(error_msg)
                if api_error_handler:
                    api_error_handler.handle_validation_error(ValueError(error_msg), {
                        "analysis_type": analysis_type,
                        "current_sessions": current_sessions,
                        "max_sessions": self._max_concurrent_sessions
                    })
                raise ValueError(error_msg)
            
            # 生成唯一的会话密钥
            session_key = str(uuid.uuid4())
            logger.debug(f"Generated session key: {session_key}")
            
            try:
                # 创建处理器实例
                processor = ProcessorFactory.create_processor(analysis_type)
                
                # 创建会话
                session = Session(
                    session_key=session_key,
                    analysis_type=analysis_type,
                    processor=processor,
                    status=SessionStatus.CREATED
                )
                
                # 存储会话
                self._sessions[session_key] = session
                
                logger.info(f"Created session {session_key} with analysis_type={analysis_type}")
                return session_key
                
            except Exception as e:
                logger.error(f"Failed to create session with analysis_type={analysis_type}: {e}", exc_info=True)
                if api_error_handler:
                    api_error_handler.handle_internal_error(e, "create_session")
                raise
    
    def get_session(self, session_key: str) -> Optional[Session]:
        """
        获取会话实例
        
        Args:
            session_key: 会话密钥
            
        Returns:
            Optional[Session]: 会话实例，如果不存在则返回None
        """
        with self._lock:
            session = self._sessions.get(session_key)
            if session:
                session.update_activity()
                logger.debug(f"Retrieved session {session_key}")
            else:
                logger.warning(f"Session not found: {session_key}")
            return session
    
    @log_errors("api", "remove_session")
    def remove_session(self, session_key: str) -> bool:
        """
        移除会话
        
        Args:
            session_key: 会话密钥
            
        Returns:
            bool: 是否成功移除
        """
        with self._lock:
            try:
                session = self._sessions.pop(session_key, None)
                if session:
                    # 停止会话处理
                    session.stop_processing()
                    logger.info(f"Removed session {session_key}")
                    return True
                else:
                    logger.warning(f"Attempted to remove non-existent session: {session_key}")
                    return False
            except Exception as e:
                logger.error(f"Failed to remove session {session_key}: {e}", exc_info=True)
                if api_error_handler:
                    api_error_handler.handle_internal_error(e, "remove_session")
                raise
    
    def get_session_count(self) -> int:
        """获取当前会话数量"""
        with self._lock:
            count = len(self._sessions)
            logger.debug(f"Current session count: {count}")
            return count
    
    def get_session_status(self, session_key: str) -> Optional[Dict[str, any]]:
        """
        获取会话状态信息
        
        Args:
            session_key: 会话密钥
            
        Returns:
            Optional[Dict]: 会话状态信息，如果会话不存在则返回None
        """
        with self._lock:
            session = self._sessions.get(session_key)
            if not session:
                logger.warning(f"Session status requested for non-existent session: {session_key}")
                return None
            
            status_info = {
                "session_key": session.session_key,
                "analysis_type": session.analysis_type,
                "status": session.status.value,
                "created_at": session.created_at.isoformat(),
                "last_activity": session.last_activity.isoformat(),
                "processing_active": session.processing_task is not None and not session.processing_task.done()
            }
            
            logger.debug(f"Session status for {session_key}: {status_info['status']}")
            return status_info
    
    def list_sessions(self) -> List[Dict[str, any]]:
        """
        列出所有会话的状态信息
        
        Returns:
            List[Dict]: 所有会话的状态信息列表
        """
        with self._lock:
            session_list = [
                {
                    "session_key": session.session_key,
                    "analysis_type": session.analysis_type,
                    "status": session.status.value,
                    "created_at": session.created_at.isoformat(),
                    "last_activity": session.last_activity.isoformat(),
                    "processing_active": session.processing_task is not None and not session.processing_task.done()
                }
                for session in self._sessions.values()
            ]
            
            logger.debug(f"Listed {len(session_list)} sessions")
            return session_list
    
    def cleanup_expired_sessions(self) -> int:
        """
        清理过期的会话
        
        Returns:
            int: 清理的会话数量
        """
        expired_sessions = []
        
        with self._lock:
            for session_key, session in self._sessions.items():
                if session.is_expired(self._session_timeout):
                    expired_sessions.append(session_key)
        
        # 移除过期会话
        cleaned_count = 0
        for session_key in expired_sessions:
            if self.remove_session(session_key):
                cleaned_count += 1
        
        if cleaned_count > 0:
            logger.info(f"Cleaned up {cleaned_count} expired sessions")
        
        return cleaned_count
    
    @log_errors("api", "cleanup_loop")
    async def _cleanup_loop(self) -> None:
        """定期清理过期会话的后台任务"""
        logger.info("Starting cleanup loop")
        try:
            while self._running:
                try:
                    cleaned_count = self.cleanup_expired_sessions()
                    logger.debug(f"Cleanup loop completed, cleaned {cleaned_count} sessions")
                    await asyncio.sleep(60)  # 每分钟检查一次
                except asyncio.CancelledError:
                    logger.info("Cleanup loop cancelled")
                    raise  # 重新抛出CancelledError以正确处理取消
                except Exception as e:
                    logger.error(f"Error in cleanup loop: {e}", exc_info=True)
                    if api_error_handler:
                        api_error_handler.handle_internal_error(e, "cleanup_loop")
                    await asyncio.sleep(60)
        except asyncio.CancelledError:
            logger.info("Cleanup loop cancelled")
        finally:
            logger.info("Cleanup loop finished")
    
    async def _cleanup_all_sessions(self) -> None:
        """清理所有会话"""
        session_keys = list(self._sessions.keys())
        logger.info(f"Cleaning up all {len(session_keys)} sessions")
        for session_key in session_keys:
            self.remove_session(session_key)
        logger.info(f"Cleaned up all {len(session_keys)} sessions")


# 注册默认处理器
def register_default_processors():
    """注册默认的处理器类型"""
    from processors import register_all_processors

    register_all_processors(ProcessorFactory)
    logger.info("Registered default processors")


# 全局会话管理器实例
session_manager = SessionManager()

# 注册默认处理器
register_default_processors()