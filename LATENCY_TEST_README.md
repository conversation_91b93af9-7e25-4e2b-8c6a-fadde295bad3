# 延迟测试客户端使用说明

## 概述

延迟测试客户端 (`latency_test_client.py`) 是一个专门用于测试RTMP视频处理系统逐帧延迟的工具。它能够：

1. 使用OpenCV检测默认摄像头的分辨率、帧率和画面
2. 用FFmpeg以原始数据逐帧推流到RTMP服务器
3. 记录发送每一帧的时间戳
4. 根据接收到的`analysis_data`中的`frame_number`判断得到的是第几帧数据
5. 计算逐帧延迟并实时绘制成图像
6. 生成详细的延迟分析报告

## 功能特点

- **实时延迟监控**: 显示实时更新的延迟图表
- **精确帧同步**: 通过frame_number精确匹配发送和接收的帧
- **详细统计分析**: 生成包含平均值、最值、标准差等统计信息的报告
- **自动摄像头检测**: 自动检测摄像头参数并列出可用设备
- **可视化报告**: 生成包含时间序列图和分布直方图的PNG报告

## 安装依赖

确保已安装所有必需的依赖包：

```bash
# 激活虚拟环境（Windows）
& C:\Users\<USER>\Repostories\media_process_server\.venv\Scripts\Activate.ps1

# 安装依赖
pip install opencv-python matplotlib numpy httpx requests sseclient-py
```

或者使用requirements.txt：

```bash
pip install -r requirements.txt
```

## 使用前准备

### 1. 启动服务器

确保RTMP视频处理服务器已启动：

```bash
# 启动主服务器
python -m server.main
```

### 2. 验证环境

运行验证脚本检查环境是否正确配置：

```bash
python test_latency_client.py
```

此脚本将验证：
- 依赖库是否已安装
- 摄像头是否可用
- API服务器是否运行
- FFmpeg是否能访问摄像头
- 图表功能是否正常

### 3. 摄像头配置

如果验证脚本显示摄像头名称不正确，请：

1. 查看验证脚本输出的可用设备列表
2. 修改`latency_test_client.py`中的`camera_names`列表
3. 将正确的摄像头名称放在列表第一位

## 运行延迟测试

### 基本使用

```bash
python latency_test_client.py
```

### 测试流程

1. **摄像头检测**: 自动检测摄像头分辨率和帧率
2. **会话创建**: 创建视频处理会话
3. **SSE连接**: 建立服务器推送事件连接
4. **实时图表**: 显示延迟监控图表窗口
5. **推流测试**: 启动摄像头推流（默认60秒）
6. **数据收集**: 实时收集和显示延迟数据
7. **报告生成**: 生成详细的延迟分析报告

### 交互选项

- **测试时长**: 程序会询问测试时长（默认60秒）
- **提前结束**: 按`Ctrl+C`可提前结束测试
- **图表查看**: 测试期间可查看实时延迟图表

## 输出文件

### 延迟报告

测试完成后会生成PNG格式的延迟报告，包含：

- **时间序列图**: 显示每帧的延迟变化
- **分布直方图**: 显示延迟值的分布情况
- **统计信息**: 平均值、最值、标准差等

文件命名格式：`latency_report_YYYYMMDD_HHMMSS.png`

### 控制台输出

测试过程中会显示：
- 实时延迟信息（每帧）
- 摄像头参数
- 连接状态
- 统计摘要

## 故障排除

### 常见问题

1. **摄像头无法访问**
   - 确保摄像头未被其他程序占用
   - 检查摄像头驱动是否正常
   - 运行验证脚本查看可用设备

2. **FFmpeg错误**
   - 确保FFmpeg已安装并添加到PATH
   - 检查摄像头名称是否正确
   - 尝试不同的摄像头名称

3. **API连接失败**
   - 确保服务器已启动
   - 检查端口是否被占用
   - 验证防火墙设置

4. **图表显示问题**
   - 确保matplotlib已正确安装
   - 检查显示器配置
   - 尝试更新matplotlib版本

### 调试技巧

1. **查看详细日志**：
   ```bash
   python latency_test_client.py 2>&1 | tee latency_test.log
   ```

2. **测试单个组件**：
   ```bash
   python test_latency_client.py
   ```

3. **检查摄像头设备**：
   ```bash
   ffmpeg -f dshow -list_devices true -i dummy
   ```

## 配置选项

### 修改摄像头设备

在`latency_test_client.py`中修改：

```python
camera_names = [
    "你的摄像头名称",  # 将正确的名称放在第一位
    "Integrated Camera",
    "USB Camera", 
    # ... 其他名称
]
```

### 修改服务器地址

```python
client = LatencyTestClient(
    api_base_url="http://your-server:8000",
    rtmp_url="rtmp://your-server:1935"
)
```

### 修改测试参数

```python
# 修改默认测试时长
duration = 120  # 120秒

# 修改延迟窗口大小
self.latency_window = deque(maxlen=200)  # 显示最近200帧
```

## 性能建议

1. **关闭不必要的程序**：测试期间关闭其他占用摄像头或网络的程序
2. **使用有线网络**：避免WiFi可能带来的网络延迟波动
3. **充足的系统资源**：确保CPU和内存资源充足
4. **稳定的光照条件**：避免光照变化影响视频编码

## 结果解读

### 延迟指标

- **平均延迟**: 整体系统性能指标
- **最小延迟**: 系统最佳性能
- **最大延迟**: 系统最差性能
- **标准差**: 延迟稳定性指标

### 正常范围

- **优秀**: < 100ms
- **良好**: 100-200ms  
- **可接受**: 200-500ms
- **需要优化**: > 500ms

### 影响因素

- 网络延迟
- 视频编码时间
- 服务器解码与处理时间
- 系统负载
- 摄像头性能
