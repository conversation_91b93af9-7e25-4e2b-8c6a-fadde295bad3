"""
Processors package - 视频处理器模块

提供各种视频处理器的实现，每个处理器在独立文件中定义。
本模块作为统一入口，导入所有处理器类并提供注册功能。
"""

# 导入所有处理器类
from .opencv_preview_processor import OpenCVPreviewProcessor
from .dummy_processor import DummyProcessor

# 导出所有处理器类，供外部使用
__all__ = [
    'OpenCVPreviewProcessor',
    'DummyProcessor',
]


def get_all_processors():
    """
    获取所有可用的处理器类

    Returns:
        Dict[str, type]: 处理器名称到类的映射
    """
    return {
        'opencv_preview': OpenCVPreviewProcessor,
        'dummy': DummyProcessor,
    }


def register_all_processors(processor_factory):
    """
    将所有处理器注册到ProcessorFactory

    Args:
        processor_factory: ProcessorFactory实例
    """
    processors = get_all_processors()
    for name, processor_class in processors.items():
        processor_factory.register_processor(name, processor_class)