"""
OpenCV预览处理器实现
"""
from typing import Dict, Any
import numpy as np

from models import VideoProcessor
from logging_config import get_logger, get_error_handler, log_errors


logger = get_logger(__name__)
video_processing_error_handler = get_error_handler("video_processing")


class OpenCVPreviewProcessor(VideoProcessor):
    """OpenCV预览处理器 - 用于测试和演示"""
    
    def __init__(self):
        self._initialized = False
        self._frame_count = 0
    
    def initialize(self) -> None:
        """初始化处理器资源"""
        try:
            import cv2
            self._cv2 = cv2
            self._initialized = True
            self._frame_count = 0
            logger.info("OpenCVPreviewProcessor initialized")
        except ImportError as e:
            logger.error("OpenCV not available, processor will work in basic mode")
            if video_processing_error_handler:
                video_processing_error_handler.handle_processor_initialization_error("OpenCVPreviewProcessor", e)
            self._cv2 = None
            self._initialized = True
    
    @log_errors("video_processing", "process_frame")
    def process_frame(self, frame: np.ndarray) -> Dict[str, Any]:
        """
        处理视频帧，返回帧信息和基本分析数据
        
        Args:
            frame: 视频帧数据
            
        Returns:
            Dict[str, Any]: 分析结果
        """
        if not self._initialized:
            error_msg = "Processor not initialized"
            logger.error(error_msg)
            if video_processing_error_handler:
                video_processing_error_handler.handle_processor_initialization_error("OpenCVPreviewProcessor", RuntimeError(error_msg))
            raise RuntimeError(error_msg)
        
        self._frame_count += 1
        logger.debug(f"Processing frame {self._frame_count}")
        
        # 基本帧信息
        height, width = frame.shape[:2]
        channels = frame.shape[2] if len(frame.shape) > 2 else 1
        
        analysis_data = {
            "frame_number": self._frame_count,
            "dimensions": {
                "width": int(width),
                "height": int(height),
                "channels": int(channels)
            },
            "data_type": str(frame.dtype),
            "size_bytes": int(frame.nbytes)
        }
        
        logger.debug(f"Frame info: {width}x{height}x{channels}, dtype: {frame.dtype}")
        
        # 如果有OpenCV，添加更多分析
        if self._cv2 is not None:
            try:
                # 计算基本统计信息
                mean_val = np.mean(frame)
                std_val = np.std(frame)
                
                analysis_data.update({
                    "statistics": {
                        "mean": float(mean_val),
                        "std": float(std_val),
                        "min": float(np.min(frame)),
                        "max": float(np.max(frame))
                    }
                })
                
                logger.debug(f"Basic statistics: mean={mean_val:.2f}, std={std_val:.2f}")
                
                # 如果是彩色图像，转换为灰度并计算直方图
                if channels == 3:
                    gray = self._cv2.cvtColor(frame, self._cv2.COLOR_BGR2GRAY)
                    hist = self._cv2.calcHist([gray], [0], None, [256], [0, 256])
                    
                    analysis_data["histogram"] = {
                        "bins": 256,
                        "peak_value": int(np.argmax(hist)),
                        "peak_count": int(np.max(hist))
                    }
                    
                    logger.debug(f"Histogram: peak_value={int(np.argmax(hist))}, peak_count={int(np.max(hist))}")
                
            except Exception as e:
                logger.warning(f"Error in advanced analysis: {e}", exc_info=True)
                if video_processing_error_handler:
                    video_processing_error_handler.handle_frame_processing_error("unknown", e, {
                        "frame_number": self._frame_count,
                        "frame_shape": frame.shape
                    })
        
        logger.debug(f"Processed frame {self._frame_count}: {width}x{height}x{channels}")
        return analysis_data
    
    @log_errors("video_processing", "cleanup")
    def cleanup(self) -> None:
        """清理处理器资源"""
        if self._initialized:
            logger.info(f"OpenCVPreviewProcessor cleaned up after processing {self._frame_count} frames")
            self._initialized = False
            self._frame_count = 0
