"""
虚拟处理器实现
"""
from typing import Dict, Any
import numpy as np

from models import VideoProcessor
from logging_config import get_logger, get_error_handler, log_errors


logger = get_logger(__name__)
video_processing_error_handler = get_error_handler("video_processing")


class DummyProcessor(VideoProcessor):
    """虚拟处理器 - 用于测试，不需要任何依赖"""
    
    def __init__(self):
        self._initialized = False
        self._frame_count = 0
    
    def initialize(self) -> None:
        """初始化处理器资源"""
        try:
            self._initialized = True
            self._frame_count = 0
            logger.info("DummyProcessor initialized")
        except Exception as e:
            logger.error("Failed to initialize DummyProcessor")
            if video_processing_error_handler:
                video_processing_error_handler.handle_processor_initialization_error("DummyProcessor", e)
            raise
    
    @log_errors("video_processing", "process_frame")
    def process_frame(self, frame: np.ndarray) -> Dict[str, Any]:
        """
        处理视频帧，返回基本信息
        
        Args:
            frame: 视频帧数据
            
        Returns:
            Dict[str, Any]: 分析结果
        """
        if not self._initialized:
            error_msg = "Processor not initialized"
            if video_processing_error_handler:
                video_processing_error_handler.handle_processor_initialization_error("DummyProcessor", RuntimeError(error_msg))
            raise RuntimeError(error_msg)
        
        self._frame_count += 1
        
        # 基本帧信息
        height, width = frame.shape[:2]
        channels = frame.shape[2] if len(frame.shape) > 2 else 1
        
        return {
            "processor_type": "dummy",
            "frame_number": self._frame_count,
            "dimensions": {
                "width": int(width),
                "height": int(height),
                "channels": int(channels)
            },
            "timestamp": self._frame_count * 0.033,  # 假设30fps
            "message": f"Processed frame {self._frame_count}"
        }
    
    @log_errors("video_processing", "cleanup")
    def cleanup(self) -> None:
        """清理处理器资源"""
        if self._initialized:
            logger.info(f"DummyProcessor cleaned up after processing {self._frame_count} frames")
            self._initialized = False
            self._frame_count = 0
