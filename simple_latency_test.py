#!/usr/bin/env python3
"""
简化版延迟测试客户端

专注于核心功能：
1. OpenCV逐帧获取摄像头图像
2. FFmpeg推流每一帧
3. 记录发送时间戳
4. 计算延迟并生成报告
"""

import asyncio
import json
import time
import subprocess
import cv2
import numpy as np
import matplotlib
import matplotlib.pyplot as plt
from collections import deque
from datetime import datetime
from typing import Dict, List, Optional
import sys
import signal
import threading
import psutil

import httpx
from httpx_sse import aconnect_sse


class SimpleLatencyTest:
    """简化版延迟测试客户端"""
    
    def __init__(self, api_base_url: str = "http://*************:8000", 
                 rtmp_url: str = "rtmp://*************:1935"):
        self.api_base_url = api_base_url.rstrip('/')
        self.rtmp_url = rtmp_url.rstrip('/')
        self.session_key: Optional[str] = None
        self.running = True
        
        # 延迟测试数据
        self.frame_send_times: Dict[int, float] = {}
        self.frame_latencies: List[tuple] = []  # (frame_number, latency_ms)
        self.prev_send_time: Optional[float] = None

        # 性能监控数据
        self.system_stats = deque(maxlen=3600)  # (timestamp, cpu_%, mem_%)
        self.capture_times = []  # 摄像头读取耗时
        self.write_times = []    # ffmpeg写入耗时
        self.inter_frame_delays = [] # 实际帧间发送间隔
        
        # 摄像头参数
        self.camera_width = 640
        self.camera_height = 480
        self.camera_fps = 30
        
        # 设置信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """处理退出信号"""
        print(f"\n收到退出信号，正在停止...")
        self.running = False
    
    async def create_session(self) -> str:
        """创建视频处理会话"""
        print("创建处理会话...")
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.api_base_url}/api/session",
                json={"analysis_type": "opencv_preview"},
                timeout=10.0
            )
            response.raise_for_status()
            
            data = response.json()
            self.session_key = data["session_key"]
            print(f"✓ 会话创建成功: {self.session_key}")
            return self.session_key
    
    async def start_sse_connection_async(self):
        """启动SSE连接 (异步)"""
        print("启动异步SSE连接...")
        
        try:
            url = f"{self.api_base_url}/events/{self.session_key}"
            async with aconnect_sse(httpx.AsyncClient(), "GET", url, timeout=None) as sse_client:
                print("✓ SSE连接建立成功")
                async for event in sse_client.aiter_sse():
                    if not self.running:
                        break
                    
                    if event.event == "result":
                        self._handle_result_event(event)

        except Exception as e:
            if self.running:
                print(f"SSE连接错误: {e}")
    
    def _handle_result_event(self, event):
        """处理结果事件"""
        try:
            receive_time = time.time()
            data = json.loads(event.data)
            
            analysis_data = data.get('analysis_data', {})
            frame_number = analysis_data.get('frame_number')
            
            if frame_number and frame_number in self.frame_send_times:
                send_time = self.frame_send_times[frame_number]
                latency_ms = (receive_time - send_time) * 1000
                
                self.frame_latencies.append((frame_number, latency_ms))
                
                # 每50帧显示一次进度
                if len(self.frame_latencies) % 50 == 0:
                    print(f"已接收 {len(self.frame_latencies)} 个延迟数据，最新延迟: {latency_ms:.2f}ms")
                
        except Exception as e:
            print(f"处理结果事件失败: {e}")
    
    def detect_camera(self):
        """检测摄像头"""
        print("检测摄像头...")
        cap = cv2.VideoCapture(0)
        if not cap.isOpened():
            raise RuntimeError("无法打开摄像头")
        
        self.camera_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        self.camera_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        self.camera_fps = int(cap.get(cv2.CAP_PROP_FPS)) or 30
        
        cap.release()
        print(f"✓ 摄像头: {self.camera_width}x{self.camera_height} @ {self.camera_fps}fps")
    
    def start_opencv_stream(self, duration: int = 60):
        """启动OpenCV推流并监控性能"""
        rtmp_url = f"{self.rtmp_url}/live/{self.session_key}"
        print(f"启动OpenCV推流到: {rtmp_url}")

        def log_ffmpeg_output(pipe, pipe_name):
            """读取并打印ffmpeg的输出"""
            try:
                for line in iter(pipe.readline, b''):
                    if not self.running:
                        break
                    print(f"[FFMPEG-{pipe_name}] {line.decode('utf-8', errors='ignore').strip()}")
            except Exception as e:
                if self.running:
                    print(f"读取ffmpeg {pipe_name} 管道时出错: {e}")
            finally:
                pipe.close()

        def monitor_system_stats():
            """定期记录系统CPU和内存使用率"""
            while self.running:
                try:
                    cpu_percent = psutil.cpu_percent(interval=None)
                    mem_percent = psutil.virtual_memory().percent
                    self.system_stats.append((time.time(), cpu_percent, mem_percent))
                    time.sleep(0.5) # 每0.5秒记录一次
                except psutil.NoSuchProcess:
                    break # 主进程退出时停止

        def stream_worker():
            cap = None
            ffmpeg_process = None
            
            try:
                cap = cv2.VideoCapture(0)
                if not cap.isOpened():
                    raise RuntimeError("无法打开摄像头")
                
                cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.camera_width)
                cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.camera_height)
                cap.set(cv2.CAP_PROP_FPS, self.camera_fps)
                
                ffmpeg_cmd = [
                    "ffmpeg", "-y", "-f", "rawvideo", "-vcodec", "rawvideo",
                    "-pix_fmt", "bgr24", "-s", f"{self.camera_width}x{self.camera_height}",
                    "-r", str(self.camera_fps), "-i", "-",
                    "-c:v", "libx264", "-preset", "ultrafast", "-tune", "zerolatency",
                    "-g", str(self.camera_fps), "-f", "flv", rtmp_url
                ]
                
                ffmpeg_process = subprocess.Popen(
                    ffmpeg_cmd, stdin=subprocess.PIPE, stdout=subprocess.PIPE, stderr=subprocess.PIPE
                )
                print(f"✓ FFmpeg进程启动 (PID: {ffmpeg_process.pid})")

                # 启动FFmpeg日志监控线程
                threading.Thread(target=log_ffmpeg_output, args=(ffmpeg_process.stdout, "stdout"), daemon=True).start()
                threading.Thread(target=log_ffmpeg_output, args=(ffmpeg_process.stderr, "stderr"), daemon=True).start()
                
                frame_interval = 1.0 / self.camera_fps
                start_time = time.time()
                frame_count = 0
                
                print("开始推流...")
                while self.running and time.time() - start_time < duration:
                    loop_start_time = time.time()
                    
                    t_before_read = time.time()
                    ret, frame = cap.read()
                    t_after_read = time.time()
                    
                    if not ret: break
                    
                    frame_count += 1
                    send_time = time.time()

                    if self.prev_send_time:
                        self.inter_frame_delays.append((send_time - self.prev_send_time) * 1000)
                    self.prev_send_time = send_time
                    self.frame_send_times[frame_count] = send_time
                    
                    t_before_write = time.time()
                    try:
                        ffmpeg_process.stdin.write(frame.tobytes())
                        ffmpeg_process.stdin.flush()
                    except (BrokenPipeError, OSError):
                        print("FFmpeg管道写入错误，推流中断。")
                        break
                    t_after_write = time.time()

                    # 不再使用人工sleep，依赖管道反压和摄像头读取的阻塞来自动调节帧率

                    # 记录各项耗时
                    self.capture_times.append((t_after_read - t_before_read) * 1000)
                    self.write_times.append((t_after_write - t_before_write) * 1000)

                print(f"推流完成，总共 {frame_count} 帧")
                
            except Exception as e:
                print(f"推流错误: {e}")
            finally:
                self.running = False # 确保其他线程也退出
                if cap: cap.release()
                if ffmpeg_process:
                    try:
                        if ffmpeg_process.stdin: ffmpeg_process.stdin.close()
                        ffmpeg_process.terminate()
                        ffmpeg_process.wait(timeout=2)
                    except:
                        if ffmpeg_process.poll() is None:
                            ffmpeg_process.kill()
        
        # 启动核心工作线程
        stream_thread = threading.Thread(target=stream_worker, daemon=True)
        stream_thread.start()
        
        # 启动系统监控线程
        monitor_thread = threading.Thread(target=monitor_system_stats, daemon=True)
        monitor_thread.start()

        return stream_thread
    
    def generate_report(self):
        """生成包含性能监控的详细报告"""
        if not self.frame_latencies:
            print("没有延迟数据，无法生成报告。")
            return

        frames, latencies = zip(*self.frame_latencies)
        num_frames = len(frames)
        
        # 统计信息
        avg_latency = np.mean(latencies)
        min_latency = np.min(latencies)
        max_latency = np.max(latencies)
        std_latency = np.std(latencies)
        p95_latency = np.percentile(latencies, 95)

        print(f"\n--- 延迟统计 ---")
        print(f"  总帧数: {num_frames}")
        print(f"  平均延迟: {avg_latency:.2f} ms")
        print(f"  P95 延迟: {p95_latency:.2f} ms")
        print(f"  最小延迟: {min_latency:.2f} ms")
        print(f"  最大延迟: {max_latency:.2f} ms")
        print(f"  标准差: {std_latency:.2f} ms")

        # 生成图表
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"latency_report_{timestamp}.png"
        
        fig = plt.figure(figsize=(18, 16))
        gs = fig.add_gridspec(4, 2)

        # 1. 延迟时间序列图
        ax1 = fig.add_subplot(gs[0, :])
        ax1.plot(frames, latencies, 'b-', linewidth=1, alpha=0.8, label='每帧延迟')
        ax1.axhline(y=avg_latency, color='r', linestyle='--', label=f'平均: {avg_latency:.2f}ms')
        ax1.axhline(y=p95_latency, color='orange', linestyle='--', label=f'P95: {p95_latency:.2f}ms')
        ax1.set_title(f'视频处理延迟分析 (总帧数: {num_frames})', fontsize=14)
        ax1.set_xlabel('帧号')
        ax1.set_ylabel('延迟 (ms)')
        ax1.grid(True, alpha=0.4)
        ax1.legend()

        # 2. 延迟分布直方图
        ax2 = fig.add_subplot(gs[1, 0])
        ax2.hist(latencies, bins=50, alpha=0.75, color='skyblue', edgecolor='black')
        ax2.axvline(x=avg_latency, color='r', linestyle='--', label=f'平均: {avg_latency:.2f}ms')
        ax2.set_title('延迟分布')
        ax2.set_xlabel('延迟 (ms)')
        ax2.set_ylabel('频次')
        ax2.grid(True, alpha=0.4)
        ax2.legend()

        # 3. 系统资源监控图
        ax3 = fig.add_subplot(gs[1, 1])
        if self.system_stats:
            stats_time, cpu_p, mem_p = zip(*self.system_stats)
            start_time = stats_time[0]
            relative_time = [t - start_time for t in stats_time]
            
            ax3.plot(relative_time, cpu_p, 'g-', label='CPU 使用率 (%)')
            ax3.set_xlabel('测试时间 (秒)')
            ax3.set_ylabel('CPU 使用率 (%)', color='g')
            ax3.tick_params(axis='y', labelcolor='g')
            ax3.set_ylim(0, 100)
            
            ax3b = ax3.twinx()
            ax3b.plot(relative_time, mem_p, 'm-', alpha=0.6, label='内存使用率 (%)')
            ax3b.set_ylabel('内存使用率 (%)', color='m')
            ax3b.tick_params(axis='y', labelcolor='m')
            ax3b.set_ylim(0, 100)
            
            ax3.set_title('系统资源使用率')
            ax3.grid(True, alpha=0.4)
        else:
            ax3.text(0.5, 0.5, "无系统资源数据", ha='center', va='center')

        # 4. 推流循环各阶段耗时
        ax4 = fig.add_subplot(gs[2, :])
        if self.capture_times:
            num_perf_frames = min(len(self.capture_times), len(self.write_times))
            perf_frames = range(1, num_perf_frames + 1)
            ax4.stackplot(perf_frames, self.capture_times[:num_perf_frames], self.write_times[:num_perf_frames],
                          labels=['摄像头读取', 'FFmpeg写入'],
                          alpha=0.8)
            ax4.set_title('推流循环各阶段耗时')
            ax4.set_xlabel('帧号')
            ax4.set_ylabel('耗时 (ms)')
            ax4.legend(loc='upper left')
            ax4.grid(True, alpha=0.4)
        else:
            ax4.text(0.5, 0.5, "无耗时数据", ha='center', va='center')

        # 5. 帧间隔延迟
        ax5 = fig.add_subplot(gs[3, :])
        if self.inter_frame_delays:
            ax5.plot(range(1, len(self.inter_frame_delays) + 1), self.inter_frame_delays, 'c-', label='帧间发送间隔')
            target_interval = 1000 / self.camera_fps
            ax5.axhline(y=target_interval, color='grey', linestyle='--', label=f'目标间隔: {target_interval:.2f}ms')
            ax5.set_title('实际帧间发送间隔')
            ax5.set_xlabel('帧号')
            ax5.set_ylabel('间隔 (ms)')
            ax5.legend()
            ax5.grid(True, alpha=0.4)
        else:
            ax5.text(0.5, 0.5, "无帧间隔数据", ha='center', va='center')

        # 添加统计文本
        stats_text = f"""--- 关键统计 ---
平均延迟: {avg_latency:.2f} ms
P95 延迟: {p95_latency:.2f} ms
最大延迟: {max_latency:.2f} ms
标准差: {std_latency:.2f} ms
总帧数: {num_frames}"""
        fig.text(0.5, 0.01, stats_text, ha='center', fontsize=10,
                 bbox=dict(boxstyle="round,pad=0.5", fc='lightgray', alpha=0.6))
        
        plt.tight_layout(rect=[0, 0.05, 1, 0.98])
        plt.suptitle('端到端延迟及性能分析报告', fontsize=18, y=0.99)
        plt.savefig(filename, dpi=200, bbox_inches='tight')
        print(f"✓ 详细报告已保存: {filename}")
    
    async def cleanup(self):
        """清理资源"""
        self.running = False
        if self.session_key:
            try:
                async with httpx.AsyncClient() as client:
                    await client.delete(f"{self.api_base_url}/api/session/{self.session_key}")
                print("✓ 会话已删除")
            except:
                pass


async def main():
    """主函数"""
    print("=" * 60)
    print("简化版延迟测试客户端")
    print("=" * 60)
    
    test = SimpleLatencyTest()
    
    try:
        # 1. 检测摄像头
        test.detect_camera()
        
        # 2. 创建会话
        await test.create_session()
        
        # 3. 启动SSE连接 (后台任务)
        sse_task = asyncio.create_task(test.start_sse_connection_async())
        
        # 4. 询问测试时长
        duration_input = input("测试时长（秒，默认30）: ").strip()
        duration = int(duration_input) if duration_input else 30
        
        # 5. 启动推流
        stream_thread = test.start_opencv_stream(duration)
        
        # 6. 等待完成
        print(f"等待测试完成 ({duration}秒)...")
        for i in range(duration):
            if not test.running:
                break
            await asyncio.sleep(1)
            if (i + 1) % 10 == 0:
                print(f"进度: {i + 1}/{duration}秒，延迟数据: {len(test.frame_latencies)}")
        
        # 7. 等待剩余结果
        print("等待剩余结果...")
        await asyncio.sleep(3)
        
        # 8. 生成报告
        sse_task.cancel()  # 停止SSE任务
        test.generate_report()
        
    except KeyboardInterrupt:
        print("\n用户中断")
    except Exception as e:
        print(f"错误: {e}")
    finally:
        await test.cleanup()
        print("测试完成")


if __name__ == "__main__":
    matplotlib.rc("font", family='Source Han Sans SC')
    asyncio.run(main())
